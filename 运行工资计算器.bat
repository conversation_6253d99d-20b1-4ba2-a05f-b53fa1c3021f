@echo off
chcp 65001 >nul 2>&1
title 便携版工资计算器v2

echo ===================================
echo     便携版工资计算器v2
echo     带文件选择和验算功能
echo ===================================
echo.
echo 正在启动，请稍候...
echo.

REM 检查Python是否安装
echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ 未找到Python环境
    echo.
    echo 💡 解决方案：
    echo 1. 安装Python 3.6+ 从 https://python.org
    echo 2. 或者使用 dist\便携版工资计算器.exe （无需Python）
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 检查pandas是否安装
python -c "import pandas" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 未安装pandas，正在安装...
    pip install pandas openpyxl xlrd
    if %errorlevel% neq 0 (
        echo ❌ pandas安装失败
        echo 程序将以CSV模式运行（需要手动转换Excel文件）
        echo.
    ) else (
        echo ✅ pandas安装成功
        echo.
    )
)

echo 🚀 启动工资计算器v2（带文件选择功能）...
echo.
python "便携版工资计算器v2.py"

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序运行出错
    echo 错误代码: %errorlevel%
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查文件 "便携版工资计算器v2.py" 是否存在
    echo 2. 检查Python环境是否正常
    echo 3. 或者使用 dist\便携版工资计算器.exe
    echo.
)

echo.
echo 程序已结束，按任意键退出...
pause >nul
