# 自动工资计算系统使用说明

## 📋 系统概述

这个自动工资计算系统可以从财务表中自动计算每个员工的工资总额，并更新到工资表中，大大简化了工资核算工作。

## 🎯 主要功能

1. **自动计算工资**: 从财务表中按员工代号汇总实发工资
2. **自动更新工资表**: 将计算结果更新到支付宝批量付款模板
3. **数据备份**: 自动备份原始工资表文件
4. **工资排行**: 显示员工工资排行榜
5. **错误处理**: 完善的错误处理和提示

## 📁 文件说明

### 输入文件
- `财务表.xlsx`: 包含所有代练订单的详细记录
- `工资表.xls`: 支付宝批量付款文件模板

### 输出文件
- `工资表_更新.xlsx`: 更新后的工资表
- `工资表_备份_[时间戳].xls`: 原工资表的备份
- `工资更新报告_[时间戳].txt`: 详细的更新报告

## 🚀 使用方法

### 方法一：使用简化版（推荐）

```bash
python 简化工资计算器.py
```

### 方法二：使用完整版

```bash
python auto_salary_calculator.py
```

## 📊 数据对应关系

| 财务表字段 | 工资表字段 | 说明 |
|-----------|-----------|------|
| pw姓名 | 备注（选填） | 员工代号，如"小里-顾乔" |
| 实发工资 | 金额（必填，单位：元） | 工资金额 |
| - | 收款方姓名（必填） | 员工真实姓名 |
| - | 收款方支付宝账号（必填） | 支付宝账号 |

## ⚠️ 注意事项

1. **文件位置**: 确保`财务表.xlsx`和`工资表.xls`在同一目录下
2. **数据格式**: 财务表中的"pw姓名"必须与工资表中的"备注"字段一致
3. **备份重要**: 系统会自动备份原文件，但建议手动备份重要数据
4. **新员工**: 如果财务表中有新员工，系统会提示需要手动添加支付宝信息

## 🔧 系统要求

- Python 3.6+
- pandas
- openpyxl
- xlrd

### 安装依赖

```bash
pip install pandas openpyxl xlrd
```

## 📈 运行结果示例

```
=== 简化版自动工资计算器 ===
开始时间: 2025-06-18 06:28:53
✅ 已备份原工资表为: 工资表_备份_20250618_062853.xls

📊 读取财务表并计算工资...
计算出 398 个人的工资
工资总额: 1,160,853.86 元

📝 更新工资表...
更新了 385 条工资记录

💾 保存文件...
✅ 已保存为: 工资表_更新.xlsx

🏆 工资排行榜 (前20名):
 1. 小里-顾乔                 46,527.82元
 2. 小里-77                 39,239.80元
 3. 小里-TzT                13,851.60元
 ...

✅ 工资计算完成！
```

## 🛠️ 故障排除

### 常见问题

1. **"No module named 'pandas'"**
   - 解决方案: 运行 `pip install pandas openpyxl xlrd`

2. **"文件不存在"**
   - 检查`财务表.xlsx`和`工资表.xls`是否在当前目录
   - 确认文件名拼写正确

3. **"数据格式错误"**
   - 检查财务表中是否有"pw姓名"和"实发工资"列
   - 确认工资表格式符合支付宝模板要求

4. **"更新记录数为0"**
   - 检查财务表中的"pw姓名"是否与工资表中的"备注"字段匹配

## 📞 技术支持

如果遇到问题，请检查：
1. 文件格式是否正确
2. 数据是否完整
3. Python环境是否正确安装

## 🔄 更新日志

- **v1.0**: 基础功能实现
- **v1.1**: 添加简化版本
- **v1.2**: 改进错误处理和用户体验

---

**注意**: 使用前请确保数据已备份，系统会自动备份但建议双重保险。
