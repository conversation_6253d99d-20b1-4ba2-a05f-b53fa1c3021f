# 自动工资计算系统使用说明

## 📋 系统概述

这个自动工资计算系统可以从财务表中自动计算每个员工的工资总额，并更新到工资表中，大大简化了工资核算工作。**特别增加了完善的验算功能，确保工资计算的准确性和安全性。**

## 🎯 主要功能

1. **自动计算工资**: 从财务表中按员工代号汇总实发工资
2. **自动更新工资表**: 将计算结果更新到支付宝批量付款模板
3. **🔍 多重验算**: 四重验算机制确保数据准确性
4. **数据备份**: 自动备份原始工资表文件
5. **工资排行**: 显示员工工资排行榜
6. **详细报告**: 生成完整的验算报告
7. **错误处理**: 完善的错误处理和提示

## 📁 文件说明

### 输入文件
- `财务表.xlsx`: 包含所有代练订单的详细记录
- `工资表.xls`: 支付宝批量付款文件模板

### 输出文件
- `工资表_更新.xlsx`: 更新后的工资表
- `工资表_备份_[时间戳].xls`: 原工资表的备份
- `工资验算报告.txt`: 详细的验算报告
- `工资更新报告_[时间戳].txt`: 详细的更新报告

## 🚀 使用方法

### 方法一：使用简化版（快速）

```bash
python 简化工资计算器.py
```

### 方法二：使用带验算版（推荐）⭐

```bash
python 带验算的工资计算器.py
```

### 方法三：使用完整版

```bash
python auto_salary_calculator.py
```

## 📊 数据对应关系

| 财务表字段 | 工资表字段 | 说明 |
|-----------|-----------|------|
| pw姓名 | 备注（选填） | 员工代号，如"小里-顾乔" |
| 实发工资 | 金额（必填，单位：元） | 工资金额 |
| - | 收款方姓名（必填） | 员工真实姓名 |
| - | 收款方支付宝账号（必填） | 支付宝账号 |

## 🔍 验算功能详解

### 四重验算机制

1. **数据完整性检查**
   - 验证财务表数据完整性
   - 检查员工数量和订单数量合理性
   - 确保平均订单数在合理范围内

2. **工资合理性检查**
   - 检查最高、最低、平均工资
   - 识别异常高工资（超过10万）
   - 发现负数工资等错误数据

3. **数据匹配度检查**
   - 验证财务表与工资表的匹配率
   - 确保匹配率达到80%以上
   - 统计匹配成功和失败的记录

4. **金额变化检查**
   - 对比更新前后的工资总额
   - 计算变化金额和变化率
   - 警告异常大的变化（超过50%）

### 最终验算

- **数据一致性检查**: 确保更新后的总额与预期一致
- **浮点误差控制**: 允许0.01元的计算误差
- **更新统计验证**: 验证更新、未变化、错误记录数

## ⚠️ 注意事项

1. **文件位置**: 确保`财务表.xlsx`和`工资表.xls`在同一目录下
2. **数据格式**: 财务表中的"pw姓名"必须与工资表中的"备注"字段一致
3. **备份重要**: 系统会自动备份原文件，但建议手动备份重要数据
4. **新员工**: 如果财务表中有新员工，系统会提示需要手动添加支付宝信息
5. **验算警告**: 如果验算出现警告，请仔细检查数据后再决定是否继续

## 🔧 系统要求

- Python 3.6+
- pandas
- openpyxl
- xlrd

### 安装依赖

```bash
pip install pandas openpyxl xlrd
```

## 📈 运行结果示例

### 带验算版运行示例

```
=== 带验算功能的工资计算器 ===
开始时间: 2025-06-18 06:30:54
✅ 已备份原工资表为: 工资表_备份_20250618_063054.xls

📊 第一步：读取财务表并计算工资...
财务表总记录数: 2924
有效记录数: 2921
过滤掉的记录数: 3
计算出 398 个人的工资
财务表工资总额: 1,160,853.86 元

📝 第二步：读取原工资表...
原工资表记录数: 385
原工资表总额: 1,150,625.06 元

� 第三步：执行验算...
  🔍 验算项目1: 数据完整性检查
    - 总订单数: 2921
    - 员工数量: 398
    - 平均每人订单数: 7.3
  🔍 验算项目2: 工资合理性检查
    - 最高工资: 46,527.82 元
    - 最低工资: 28.00 元
    - 平均工资: 2,916.72 元
  🔍 验算项目3: 数据匹配度检查
    - 工资表记录数: 385
    - 匹配成功数: 385
    - 匹配率: 100.0%
  🔍 验算项目4: 金额变化检查
    - 原总额: 1,150,625.06 元
    - 新总额: 1,160,853.86 元
    - 变化金额: +10,228.80 元
    - 变化率: +0.9%
✅ 验算通过！

🔍 第五步：最终验算...
  🔍 最终验算: 数据一致性检查
    - 更新后工资表总额: 1,148,513.06 元
    - 预期总额: 1,148,513.06 元
    - 差异: 0.00 元

✅ 工资计算完成！所有验算通过！
```

## 🛠️ 故障排除

### 常见问题

1. **"No module named 'pandas'"**
   - 解决方案: 运行 `pip install pandas openpyxl xlrd`

2. **"文件不存在"**
   - 检查`财务表.xlsx`和`工资表.xls`是否在当前目录
   - 确认文件名拼写正确

3. **"数据格式错误"**
   - 检查财务表中是否有"pw姓名"和"实发工资"列
   - 确认工资表格式符合支付宝模板要求

4. **"更新记录数为0"**
   - 检查财务表中的"pw姓名"是否与工资表中的"备注"字段匹配

## 📞 技术支持

如果遇到问题，请检查：
1. 文件格式是否正确
2. 数据是否完整
3. Python环境是否正确安装

## 🔄 更新日志

- **v1.0**: 基础功能实现
- **v1.1**: 添加简化版本
- **v1.2**: 改进错误处理和用户体验

---

**注意**: 使用前请确保数据已备份，系统会自动备份但建议双重保险。
