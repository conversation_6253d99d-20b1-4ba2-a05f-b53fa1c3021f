import pandas as pd
import numpy as np
from datetime import datetime
import shutil

def main():
    """
    带验算功能的自动工资计算器
    """
    
    print("=== 带验算功能的工资计算器 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 备份原文件
        backup_name = f'工资表_备份_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xls'
        shutil.copy2('工资表.xls', backup_name)
        print(f"✅ 已备份原工资表为: {backup_name}")
        
        # 2. 读取财务表并计算工资
        print("\n📊 第一步：读取财务表并计算工资...")
        finance_df = pd.read_excel('财务表.xlsx')
        
        # 过滤有效数据
        valid_data = finance_df[
            (finance_df['pw姓名'].notna()) & 
            (finance_df['实发工资'].notna()) &
            (finance_df['pw姓名'] != 'pw姓名')
        ]
        
        print(f"财务表总记录数: {len(finance_df)}")
        print(f"有效记录数: {len(valid_data)}")
        print(f"过滤掉的记录数: {len(finance_df) - len(valid_data)}")
        
        # 按姓名汇总工资和订单数
        salary_detail = valid_data.groupby('pw姓名').agg({
            '实发工资': ['sum', 'count', 'mean', 'min', 'max'],
            '单价': 'sum'
        }).round(2)
        
        # 简化列名
        salary_summary = valid_data.groupby('pw姓名')['实发工资'].sum().round(2)
        order_count = valid_data.groupby('pw姓名')['实发工资'].count()
        
        print(f"计算出 {len(salary_summary)} 个人的工资")
        print(f"财务表工资总额: {salary_summary.sum():,.2f} 元")
        
        # 3. 读取原工资表
        print("\n📝 第二步：读取原工资表...")
        payroll_df = pd.read_excel('工资表.xls', header=1)
        
        # 计算原工资表总额
        original_amount_col = '金额（必填，单位：元）'
        original_amounts = pd.to_numeric(payroll_df[original_amount_col], errors='coerce').fillna(0)
        original_total = original_amounts.sum()
        
        print(f"原工资表记录数: {len(payroll_df)}")
        print(f"原工资表总额: {original_total:,.2f} 元")
        
        # 4. 执行验算
        print("\n🔍 第三步：执行验算...")
        verification_result = perform_verification(
            salary_summary, payroll_df, valid_data, original_total
        )
        
        if not verification_result['passed']:
            print("❌ 验算未通过，请检查数据！")
            for warning in verification_result['warnings']:
                print(f"⚠️  {warning}")
            
            user_input = input("\n是否继续更新工资表？(y/N): ").strip().lower()
            if user_input != 'y':
                print("操作已取消")
                return
        else:
            print("✅ 验算通过！")
        
        # 5. 更新工资表
        print("\n📝 第四步：更新工资表...")
        updated_payroll_df, update_details = update_payroll_with_verification(
            payroll_df.copy(), salary_summary
        )
        
        # 6. 最终验算
        print("\n🔍 第五步：最终验算...")
        final_verification = perform_final_verification(
            updated_payroll_df, salary_summary, update_details, original_total
        )
        
        # 7. 保存文件
        if final_verification['passed']:
            print("\n💾 第六步：保存文件...")
            updated_payroll_df.to_excel('工资表_更新.xlsx', index=False)
            
            # 生成验算报告
            generate_verification_report(
                verification_result, final_verification, update_details, 
                salary_summary, original_total
            )
            
            print("✅ 已保存为: 工资表_更新.xlsx")
            print("✅ 已生成验算报告: 工资验算报告.txt")
        else:
            print("❌ 最终验算失败，未保存文件")
            return
        
        # 8. 显示汇总信息
        print_summary(salary_summary, update_details, original_total)
        
    except Exception as e:
        print(f"\n❌ 出现错误: {e}")
        print("请检查文件是否存在且格式正确")

def perform_verification(salary_summary, payroll_df, valid_data, original_total):
    """执行数据验算"""
    
    warnings = []
    passed = True
    
    print("  🔍 验算项目1: 数据完整性检查")
    
    # 检查1: 财务表数据完整性
    total_orders = len(valid_data)
    unique_employees = len(salary_summary)
    avg_orders_per_employee = total_orders / unique_employees if unique_employees > 0 else 0
    
    print(f"    - 总订单数: {total_orders}")
    print(f"    - 员工数量: {unique_employees}")
    print(f"    - 平均每人订单数: {avg_orders_per_employee:.1f}")
    
    if avg_orders_per_employee < 1:
        warnings.append("平均每人订单数过少，请检查数据")
        passed = False
    
    # 检查2: 工资合理性检查
    print("  🔍 验算项目2: 工资合理性检查")
    
    max_salary = salary_summary.max()
    min_salary = salary_summary.min()
    avg_salary = salary_summary.mean()
    
    print(f"    - 最高工资: {max_salary:,.2f} 元")
    print(f"    - 最低工资: {min_salary:,.2f} 元")
    print(f"    - 平均工资: {avg_salary:,.2f} 元")
    
    if max_salary > 100000:  # 10万以上
        warnings.append(f"存在异常高工资: {max_salary:,.2f} 元")
    
    if min_salary < 0:
        warnings.append(f"存在负数工资: {min_salary:,.2f} 元")
        passed = False
    
    # 检查3: 匹配度检查
    print("  🔍 验算项目3: 数据匹配度检查")
    
    payroll_remarks = set(payroll_df['备注（选填）'].dropna().astype(str))
    finance_names = set(salary_summary.index)
    
    matched_count = len(payroll_remarks & finance_names)
    total_payroll = len(payroll_remarks)
    match_rate = (matched_count / total_payroll * 100) if total_payroll > 0 else 0
    
    print(f"    - 工资表记录数: {total_payroll}")
    print(f"    - 匹配成功数: {matched_count}")
    print(f"    - 匹配率: {match_rate:.1f}%")
    
    if match_rate < 80:
        warnings.append(f"匹配率过低: {match_rate:.1f}%")
    
    # 检查4: 金额变化检查
    print("  🔍 验算项目4: 金额变化检查")
    
    new_total = salary_summary.sum()
    change_amount = new_total - original_total
    change_rate = (change_amount / original_total * 100) if original_total > 0 else 0
    
    print(f"    - 原总额: {original_total:,.2f} 元")
    print(f"    - 新总额: {new_total:,.2f} 元")
    print(f"    - 变化金额: {change_amount:+,.2f} 元")
    print(f"    - 变化率: {change_rate:+.1f}%")
    
    if abs(change_rate) > 50:  # 变化超过50%
        warnings.append(f"工资总额变化过大: {change_rate:+.1f}%")
    
    return {
        'passed': passed and len(warnings) == 0,
        'warnings': warnings,
        'stats': {
            'total_orders': total_orders,
            'unique_employees': unique_employees,
            'max_salary': max_salary,
            'min_salary': min_salary,
            'avg_salary': avg_salary,
            'match_rate': match_rate,
            'change_rate': change_rate
        }
    }

def update_payroll_with_verification(payroll_df, salary_summary):
    """带验算的工资表更新"""
    
    update_details = {
        'updated_count': 0,
        'unchanged_count': 0,
        'error_count': 0,
        'updates': []
    }
    
    for idx, row in payroll_df.iterrows():
        if pd.notna(row.get('备注（选填）')):
            remark = str(row['备注（选填）']).strip()
            
            if remark in salary_summary.index:
                old_amount = pd.to_numeric(row.get('金额（必填，单位：元）', 0), errors='coerce')
                new_amount = salary_summary[remark]
                
                if pd.isna(old_amount):
                    old_amount = 0
                
                payroll_df.at[idx, '金额（必填，单位：元）'] = new_amount
                
                update_details['updates'].append({
                    'name': row.get('收款方姓名（必填）', 'N/A'),
                    'remark': remark,
                    'old_amount': old_amount,
                    'new_amount': new_amount,
                    'difference': new_amount - old_amount
                })
                
                if abs(new_amount - old_amount) > 0.01:  # 有实际变化
                    update_details['updated_count'] += 1
                else:
                    update_details['unchanged_count'] += 1
            else:
                update_details['error_count'] += 1
    
    return payroll_df, update_details

def perform_final_verification(updated_payroll_df, salary_summary, update_details, original_total):
    """最终验算"""
    
    print("  🔍 最终验算: 数据一致性检查")
    
    # 计算更新后的总额
    updated_amounts = pd.to_numeric(
        updated_payroll_df['金额（必填，单位：元）'], errors='coerce'
    ).fillna(0)
    updated_total = updated_amounts.sum()
    
    # 预期总额（只计算匹配的部分）
    matched_remarks = set(updated_payroll_df['备注（选填）'].dropna().astype(str))
    expected_total = salary_summary[salary_summary.index.isin(matched_remarks)].sum()
    
    print(f"    - 更新后工资表总额: {updated_total:,.2f} 元")
    print(f"    - 预期总额: {expected_total:,.2f} 元")
    print(f"    - 差异: {abs(updated_total - expected_total):,.2f} 元")
    
    # 验证更新统计
    print(f"    - 更新记录数: {update_details['updated_count']}")
    print(f"    - 未变化记录数: {update_details['unchanged_count']}")
    print(f"    - 错误记录数: {update_details['error_count']}")
    
    passed = abs(updated_total - expected_total) < 0.01  # 允许0.01元的浮点误差
    
    return {
        'passed': passed,
        'updated_total': updated_total,
        'expected_total': expected_total,
        'difference': abs(updated_total - expected_total)
    }

def generate_verification_report(verification_result, final_verification, update_details, 
                               salary_summary, original_total):
    """生成验算报告"""
    
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    report = f"""
=== 工资计算验算报告 ===
生成时间: {timestamp}

📊 数据统计:
- 员工总数: {len(salary_summary)}
- 财务表工资总额: {salary_summary.sum():,.2f} 元
- 原工资表总额: {original_total:,.2f} 元
- 更新后总额: {final_verification['updated_total']:,.2f} 元

🔍 验算结果:
- 初步验算: {'通过' if verification_result['passed'] else '未通过'}
- 最终验算: {'通过' if final_verification['passed'] else '未通过'}
- 数据差异: {final_verification['difference']:,.2f} 元

📝 更新统计:
- 更新记录数: {update_details['updated_count']}
- 未变化记录数: {update_details['unchanged_count']}
- 错误记录数: {update_details['error_count']}

⚠️ 警告信息:
"""
    
    if verification_result['warnings']:
        for warning in verification_result['warnings']:
            report += f"- {warning}\n"
    else:
        report += "- 无警告\n"
    
    report += f"""
🏆 工资排行榜 (前10名):
"""
    
    top_10 = salary_summary.sort_values(ascending=False).head(10)
    for i, (name, amount) in enumerate(top_10.items(), 1):
        report += f"{i:2d}. {name:<20} {amount:>10,.2f}元\n"
    
    # 保存报告
    with open('工资验算报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

def print_summary(salary_summary, update_details, original_total):
    """打印汇总信息"""
    
    print(f"\n📊 最终汇总:")
    print(f"   - 员工总数: {len(salary_summary)}")
    print(f"   - 工资总额: {salary_summary.sum():,.2f} 元")
    print(f"   - 更新记录: {update_details['updated_count']}")
    print(f"   - 原总额: {original_total:,.2f} 元")
    print(f"   - 变化: {salary_summary.sum() - original_total:+,.2f} 元")
    
    print(f"\n🏆 工资排行榜 (前10名):")
    top_10 = salary_summary.sort_values(ascending=False).head(10)
    for i, (name, amount) in enumerate(top_10.items(), 1):
        print(f"   {i:2d}. {name:<20} {amount:>10,.2f}元")
    
    print(f"\n✅ 工资计算完成！所有验算通过！")

if __name__ == "__main__":
    main()
