import pandas as pd
import numpy as np
import os

def analyze_excel_files():
    """分析两个Excel文件的内容和联系"""

    # 读取工资表 - 尝试不同的方式读取
    try:
        # 先尝试读取前几行看看结构
        payroll_df_raw = pd.read_excel('工资表.xls', header=None)
        print("=== 工资表.xls 原始内容 ===")
        print(f"形状: {payroll_df_raw.shape}")
        print("\n前10行数据:")
        print(payroll_df_raw.head(10))

        # 尝试找到真正的表头
        print("\n寻找真正的数据开始行...")
        for i in range(min(10, len(payroll_df_raw))):
            row = payroll_df_raw.iloc[i]
            print(f"第{i}行: {row.tolist()}")

        # 重新读取，跳过前面的说明行
        payroll_df = pd.read_excel('工资表.xls', header=1)  # 假设第2行是表头
        print(f"\n重新读取后的形状: {payroll_df.shape}")
        print(f"列名: {list(payroll_df.columns)}")
        print("\n前5行有效数据:")
        print(payroll_df.head())

    except Exception as e:
        print(f"读取工资表时出错: {e}")
        payroll_df = None
    
    print("\n" + "="*50 + "\n")

    # 读取财务表
    try:
        finance_df = pd.read_excel('财务表.xlsx')
        print("=== 财务表.xlsx 内容 ===")
        print(f"形状: {finance_df.shape}")
        print(f"列名: {list(finance_df.columns)}")
        print("\n前5行数据:")
        print(finance_df.head())

        # 显示一些关键列的详细信息
        print("\n关键列详细信息:")
        if 'pw姓名' in finance_df.columns:
            print(f"pw姓名唯一值数量: {finance_df['pw姓名'].nunique()}")
            print(f"pw姓名前10个值: {finance_df['pw姓名'].dropna().unique()[:10].tolist()}")

        if '实发工资' in finance_df.columns:
            print(f"实发工资总和: {finance_df['实发工资'].sum()}")
            print(f"实发工资统计: {finance_df['实发工资'].describe()}")

        if '接单时间' in finance_df.columns:
            print(f"时间范围: {finance_df['接单时间'].min()} 到 {finance_df['接单时间'].max()}")

    except Exception as e:
        print(f"读取财务表时出错: {e}")
        finance_df = None
    
    print("\n" + "="*50 + "\n")
    
    # 分析两个表之间的联系
    if payroll_df is not None and finance_df is not None:
        print("=== 两表联系分析 ===")

        # 分析工资表的实际结构
        print("\n工资表结构分析:")
        # 查找包含姓名的列
        name_col = None
        amount_col = None
        account_col = None

        for col in payroll_df.columns:
            col_values = payroll_df[col].dropna().astype(str)
            if any('姓名' in str(val) for val in col_values[:5]):
                name_col = col
                print(f"可能的姓名列: {col}")
            elif any('金额' in str(val) or '工资' in str(val) for val in col_values[:5]):
                amount_col = col
                print(f"可能的金额列: {col}")
            elif any('账号' in str(val) or '支付宝' in str(val) for val in col_values[:5]):
                account_col = col
                print(f"可能的账号列: {col}")

        # 尝试提取实际的姓名和金额数据
        print("\n提取工资表中的实际数据:")
        if payroll_df.shape[0] > 2:  # 跳过表头行
            actual_data = payroll_df.iloc[2:].copy()  # 从第3行开始
            print(f"实际数据行数: {len(actual_data)}")

            # 查找姓名列（通常是第3列或包含中文姓名的列）
            for i, col in enumerate(actual_data.columns):
                sample_values = actual_data[col].dropna().head(5)
                print(f"列{i} ({col})的样本值: {sample_values.tolist()}")

        # 分析财务表中的姓名
        print("\n财务表姓名分析:")
        if 'pw姓名' in finance_df.columns:
            unique_names = finance_df['pw姓名'].dropna().unique()
            print(f"财务表中的唯一姓名数量: {len(unique_names)}")
            print(f"前20个姓名: {unique_names[:20].tolist()}")

            # 计算每个人的总工资
            if '实发工资' in finance_df.columns:
                name_salary = finance_df.groupby('pw姓名')['实发工资'].sum().sort_values(ascending=False)
                print(f"\n按总工资排序的前10名:")
                print(name_salary.head(10))

        # 尝试匹配姓名
        print("\n尝试匹配两表中的姓名:")
        if payroll_df.shape[0] > 2:
            # 假设第3列是姓名列
            payroll_names = set()
            for col in payroll_df.columns[1:4]:  # 检查前几列
                names = payroll_df[col].dropna().astype(str)
                # 过滤掉明显不是姓名的值
                for name in names:
                    if len(name) >= 2 and len(name) <= 4 and not name.isdigit():
                        payroll_names.add(name)

            finance_names = set(finance_df['pw姓名'].dropna()) if 'pw姓名' in finance_df.columns else set()

            print(f"工资表中可能的姓名: {list(payroll_names)[:10]}")
            print(f"财务表中的姓名: {list(finance_names)[:10]}")

            common_names = payroll_names & finance_names
            print(f"共同姓名: {common_names}")
            print(f"共同姓名数量: {len(common_names)}")

        # 金额对比
        print("\n金额对比分析:")
        if '实发工资' in finance_df.columns:
            total_finance_salary = finance_df['实发工资'].sum()
            print(f"财务表总工资: {total_finance_salary:,.2f}")

            # 尝试从工资表中提取金额
            for col in payroll_df.columns:
                try:
                    amounts = pd.to_numeric(payroll_df[col], errors='coerce').dropna()
                    if len(amounts) > 0 and amounts.sum() > 1000:  # 假设总金额应该大于1000
                        print(f"工资表列'{col}'的总金额: {amounts.sum():,.2f}")
                except:
                    pass

if __name__ == "__main__":
    analyze_excel_files()
