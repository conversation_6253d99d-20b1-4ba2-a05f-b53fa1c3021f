import pandas as pd
import numpy as np
from datetime import datetime
import shutil

def main():
    """
    简化版自动工资计算器
    从财务表自动计算工资并更新到工资表
    """
    
    print("=== 简化版自动工资计算器 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 备份原文件
        backup_name = f'工资表_备份_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xls'
        shutil.copy2('工资表.xls', backup_name)
        print(f"✅ 已备份原工资表为: {backup_name}")
        
        # 2. 读取财务表并计算工资
        print("\n📊 读取财务表并计算工资...")
        finance_df = pd.read_excel('财务表.xlsx')
        
        # 过滤有效数据并按姓名汇总
        valid_data = finance_df[
            (finance_df['pw姓名'].notna()) & 
            (finance_df['实发工资'].notna()) &
            (finance_df['pw姓名'] != 'pw姓名')
        ]
        
        salary_summary = valid_data.groupby('pw姓名')['实发工资'].sum().round(2)
        print(f"计算出 {len(salary_summary)} 个人的工资")
        print(f"工资总额: {salary_summary.sum():,.2f} 元")
        
        # 3. 读取并更新工资表
        print("\n📝 更新工资表...")
        payroll_df = pd.read_excel('工资表.xls', header=1)
        
        update_count = 0
        for idx, row in payroll_df.iterrows():
            if pd.notna(row.get('备注（选填）')):
                remark = str(row['备注（选填）']).strip()
                if remark in salary_summary.index:
                    new_amount = salary_summary[remark]
                    payroll_df.at[idx, '金额（必填，单位：元）'] = new_amount
                    update_count += 1
        
        print(f"更新了 {update_count} 条工资记录")
        
        # 4. 保存更新后的文件
        print("\n💾 保存文件...")
        payroll_df.to_excel('工资表_更新.xlsx', index=False)
        print("✅ 已保存为: 工资表_更新.xlsx")
        
        # 5. 显示工资排行榜
        print("\n🏆 工资排行榜 (前20名):")
        top_20 = salary_summary.sort_values(ascending=False).head(20)
        for i, (name, amount) in enumerate(top_20.items(), 1):
            print(f"{i:2d}. {name:<20} {amount:>10,.2f}元")
        
        print(f"\n✅ 工资计算完成！")
        print(f"📁 生成文件:")
        print(f"   - {backup_name} (原文件备份)")
        print(f"   - 工资表_更新.xlsx (更新后的工资表)")
        
    except Exception as e:
        print(f"\n❌ 出现错误: {e}")
        print("请检查文件是否存在且格式正确")

if __name__ == "__main__":
    main()
