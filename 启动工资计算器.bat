@echo off
chcp 65001 >nul 2>&1
title 便携版工资计算器v2 - 带文件选择和验算功能

echo.
echo ===================================
echo     便携版工资计算器v2
echo     带文件选择和验算功能
echo ===================================
echo.
echo ✨ 特色功能：
echo   - 智能文件选择（支持Excel/CSV）
echo   - 四重验算机制确保准确性
echo   - 自动格式转换
echo   - 详细验算报告
echo.
echo 🔍 正在检查运行环境...

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ 未找到Python环境
    echo.
    echo 💡 解决方案：
    echo 1. 安装Python 3.6+ 从 https://python.org
    echo 2. 确保Python已添加到系统PATH环境变量
    echo.
    echo 📁 或者您可以：
    echo    - 手动运行: python "便携版工资计算器v2.py"
    echo    - 在命令行中运行此脚本
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo ✅ Python环境检查通过

REM 检查pandas是否安装
echo 🔍 检查必要的库...
python -c "import pandas" >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ⚠️ 未安装pandas库，正在自动安装...
    echo 📦 安装命令: pip install pandas openpyxl xlrd
    echo.
    pip install pandas openpyxl xlrd
    if %errorlevel% neq 0 (
        echo.
        echo ❌ 自动安装失败
        echo.
        echo 💡 请手动安装：
        echo    pip install pandas openpyxl xlrd
        echo.
        echo 📝 注意：程序仍可运行，但需要手动转换Excel文件为CSV格式
        echo.
        echo 按任意键继续...
        pause >nul
    ) else (
        echo ✅ 库安装成功
    )
) else (
    echo ✅ 必要库检查通过
)

echo.
echo 🚀 启动工资计算器v2（带文件选择功能）...
echo ⏳ 请稍候，程序正在加载...
echo.

REM 运行主程序
python "便携版工资计算器v2.py"

REM 检查程序是否正常结束
if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序运行时出现错误
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查文件是否存在: 便携版工资计算器v2.py
    echo 2. 检查Python环境是否正确安装
    echo 3. 尝试在命令行中手动运行
    echo.
) else (
    echo.
    echo ✅ 程序正常结束
)

echo.
echo 按任意键退出...
pause >nul
