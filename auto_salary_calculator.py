import pandas as pd
import numpy as np
from datetime import datetime
import os
import shutil

def calculate_and_update_salary():
    """
    自动从财务表计算工资并更新到工资表
    """
    
    print("=== 自动工资计算系统 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 备份原文件
    backup_payroll_file()
    
    try:
        # 1. 读取财务表
        print("\n1. 读取财务表...")
        finance_df = pd.read_excel('财务表.xlsx')
        print(f"财务表记录数: {len(finance_df)}")
        
        # 2. 读取工资表
        print("\n2. 读取工资表...")
        payroll_df = pd.read_excel('工资表.xls', header=1)  # 跳过第一行说明
        print(f"工资表记录数: {len(payroll_df)}")
        
        # 3. 从财务表计算每个人的工资
        print("\n3. 计算财务表中每个人的工资...")
        salary_summary = calculate_salary_from_finance(finance_df)
        
        # 4. 更新工资表
        print("\n4. 更新工资表...")
        updated_payroll_df = update_payroll_table(payroll_df, salary_summary)
        
        # 5. 保存更新后的工资表
        print("\n5. 保存更新后的工资表...")
        save_updated_payroll(updated_payroll_df)
        
        # 6. 生成报告
        print("\n6. 生成更新报告...")
        generate_report(salary_summary, updated_payroll_df)
        
        print("\n✅ 工资计算和更新完成！")
        
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        print("请检查文件格式和数据完整性")

def backup_payroll_file():
    """备份原工资表文件"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'工资表_备份_{timestamp}.xls'
        shutil.copy2('工资表.xls', backup_name)
        print(f"✅ 已备份原工资表为: {backup_name}")
    except Exception as e:
        print(f"⚠️ 备份文件失败: {e}")

def calculate_salary_from_finance(finance_df):
    """从财务表计算每个人的工资"""
    
    # 过滤有效数据（排除空值和表头）
    valid_data = finance_df[
        (finance_df['pw姓名'].notna()) & 
        (finance_df['实发工资'].notna()) &
        (finance_df['pw姓名'] != 'pw姓名')  # 排除可能的表头重复
    ].copy()
    
    print(f"有效财务记录数: {len(valid_data)}")
    
    # 按姓名汇总工资
    salary_summary = valid_data.groupby('pw姓名').agg({
        '实发工资': 'sum',
        '单价': 'count'  # 统计订单数量
    }).round(2)
    
    salary_summary.columns = ['总工资', '订单数量']
    salary_summary = salary_summary.sort_values('总工资', ascending=False)
    
    print(f"计算出 {len(salary_summary)} 个人的工资")
    print(f"工资总额: {salary_summary['总工资'].sum():,.2f} 元")
    
    # 显示前10名
    print("\n前10名工资情况:")
    for name, row in salary_summary.head(10).iterrows():
        print(f"  {name}: {row['总工资']:,.2f}元 ({row['订单数量']}单)")
    
    return salary_summary

def update_payroll_table(payroll_df, salary_summary):
    """更新工资表"""
    
    updated_df = payroll_df.copy()
    
    # 确保金额列是数值类型
    amount_col = '金额（必填，单位：元）'
    if amount_col in updated_df.columns:
        updated_df[amount_col] = pd.to_numeric(updated_df[amount_col], errors='coerce')
    
    update_count = 0
    new_records = []
    
    # 遍历工资表，更新已存在的记录
    for idx, row in updated_df.iterrows():
        if pd.notna(row.get('备注（选填）')):
            remark = str(row['备注（选填）']).strip()
            
            # 在财务表汇总中查找对应的工资
            if remark in salary_summary.index:
                new_amount = salary_summary.loc[remark, '总工资']
                old_amount = row.get(amount_col, 0)
                
                updated_df.at[idx, amount_col] = new_amount
                update_count += 1
                
                print(f"更新: {remark} - {row.get('收款方姓名（必填）', 'N/A')} "
                      f"从 {old_amount} 更新为 {new_amount}")
    
    # 添加新的工资记录（在财务表中有但工资表中没有的）
    existing_remarks = set(updated_df['备注（选填）'].dropna().astype(str))
    
    for pw_name, salary_info in salary_summary.iterrows():
        if pw_name not in existing_remarks:
            # 创建新记录
            new_record = create_new_payroll_record(pw_name, salary_info['总工资'], len(updated_df) + len(new_records) + 1)
            new_records.append(new_record)
    
    # 添加新记录到工资表
    if new_records:
        new_df = pd.DataFrame(new_records)
        updated_df = pd.concat([updated_df, new_df], ignore_index=True)
        print(f"\n添加了 {len(new_records)} 条新的工资记录")
    
    print(f"\n总共更新了 {update_count} 条现有记录")
    
    return updated_df

def create_new_payroll_record(pw_name, amount, sequence_num):
    """创建新的工资记录"""
    return {
        '序号（必填）': sequence_num,
        '收款方支付宝账号（必填）': f'待填写_{pw_name}',  # 需要手动填写
        '收款方姓名（必填）': f'待填写_{pw_name}',      # 需要手动填写
        '金额（必填，单位：元）': amount,
        '备注（选填）': pw_name,
        'Unnamed: 5': np.nan,
        '注意事项': np.nan,
        '1.请勿删除或增加列。': np.nan,
        'Unnamed: 8': np.nan,
        'Unnamed: 9': np.nan,
        'Unnamed: 10': np.nan
    }

def save_updated_payroll(updated_df):
    """保存更新后的工资表"""

    try:
        # 使用openpyxl引擎保存为xlsx格式
        with pd.ExcelWriter('工资表_更新.xlsx', engine='openpyxl') as writer:
            # 写入说明行
            header_df = pd.DataFrame([
                ['支付宝批量付款文件模板（前面两行请勿删除）', '', '', '', '', '', '填写说明：', '', '', '', ''],
                ['序号（必填）', '收款方支付宝账号（必填）', '收款方姓名（必填）', '金额（必填，单位：元）', '备注（选填）', '',
                 '注意事项', '1.请勿删除或增加列。', '', '', '']
            ])

            # 合并说明行和数据
            final_df = pd.concat([header_df, updated_df], ignore_index=True)
            final_df.to_excel(writer, index=False, header=False)

        print("✅ 已保存为: 工资表_更新.xlsx")

        # 同时保存一个简化版本，只包含数据
        updated_df.to_excel('工资表_数据.xlsx', index=False)
        print("✅ 已保存数据版本为: 工资表_数据.xlsx")

    except Exception as e:
        print(f"保存Excel文件时出错: {e}")
        # 备用方案：保存为CSV
        try:
            updated_df.to_csv('工资表_更新.csv', index=False, encoding='utf-8-sig')
            print("✅ 已保存为CSV格式: 工资表_更新.csv")
        except Exception as csv_error:
            print(f"保存CSV文件也失败: {csv_error}")

def generate_report(salary_summary, updated_df):
    """生成更新报告"""
    
    report_content = f"""
=== 工资计算更新报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 统计信息:
- 财务表中的员工数量: {len(salary_summary)}
- 工资表中的记录数量: {len(updated_df)}
- 工资总额: {salary_summary['总工资'].sum():,.2f} 元
- 平均工资: {salary_summary['总工资'].mean():,.2f} 元

💰 工资排行榜 (前20名):
"""
    
    for i, (name, row) in enumerate(salary_summary.head(20).iterrows(), 1):
        report_content += f"{i:2d}. {name:<15} {row['总工资']:>10,.2f}元 ({row['订单数量']:>3d}单)\n"
    
    report_content += f"""
⚠️ 注意事项:
1. 新增记录的支付宝账号和真实姓名需要手动填写
2. 请核对金额是否正确
3. 原工资表已备份
4. 更新后的文件保存为: 工资表_更新.xls

📁 文件说明:
- 工资表_更新.xls: 更新后的工资表
- 工资表_备份_[时间戳].xls: 原工资表备份
"""
    
    # 保存报告
    with open(f'工资更新报告_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(report_content)

if __name__ == "__main__":
    calculate_and_update_salary()
