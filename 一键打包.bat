@echo off
chcp 65001 >nul
echo ===================================
echo     工资计算器一键打包工具
echo ===================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python环境
    echo 请先安装Python 3.6+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 正在检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 正在安装PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo ✅ PyInstaller准备就绪

echo.
echo 🔨 正在打包便携版工资计算器...
python -m PyInstaller --onefile --console --name="便携版工资计算器" "便携版工资计算器.py"

if %errorlevel% neq 0 (
    echo ❌ 打包失败！
    pause
    exit /b 1
)

echo.
echo ✅ 打包成功！
echo.
echo 📁 生成的可执行文件：
echo    dist\便携版工资计算器.exe
echo.
echo 📋 使用说明：
echo 1. 将exe文件复制到包含Excel文件的目录
echo 2. 将Excel文件转换为CSV格式：
echo    - 财务表.xlsx → 财务表.csv
echo    - 工资表.xls → 工资表.csv
echo 3. 双击运行exe文件
echo.
echo 📖 详细说明请查看：可执行文件使用说明.md
echo.
pause
