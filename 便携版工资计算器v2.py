"""
便携版工资计算器v2 - 带文件选择和验算功能
支持Excel和CSV文件，自动转换格式，无GUI依赖
"""

import csv
import os
import shutil
from datetime import datetime
import glob

def main():
    """主函数"""
    print("=== 便携版工资计算器v2（带验算功能）===")
    print("支持Excel(.xlsx/.xls)和CSV文件")
    print("程序将自动处理文件格式转换")
    print()
    
    # 选择文件
    finance_file = select_file_manual("财务表")
    if not finance_file:
        print("❌ 未选择财务表文件")
        input("按回车键退出...")
        return
    
    payroll_file = select_file_manual("工资表")
    if not payroll_file:
        print("❌ 未选择工资表文件")
        input("按回车键退出...")
        return
    
    print(f"✅ 财务表文件: {os.path.basename(finance_file)}")
    print(f"✅ 工资表文件: {os.path.basename(payroll_file)}")
    print()

    try:
        # 转换文件为CSV格式
        print("📄 正在处理文件格式...")
        finance_csv = convert_to_csv(finance_file, "财务表")
        payroll_csv = convert_to_csv(payroll_file, "工资表")
        
        if not finance_csv or not payroll_csv:
            print("❌ 文件格式转换失败")
            input("按回车键退出...")
            return
        
        # 备份原工资表
        backup_name = f'工资表_备份_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        shutil.copy2(payroll_csv, backup_name)
        print(f"✅ 已备份原工资表为: {backup_name}")
        
        # 读取财务表并计算工资
        print("\n📊 第一步：读取财务表并计算工资...")
        salary_data, order_count = calculate_salary_from_csv(finance_csv)
        
        if not salary_data:
            print("❌ 无法从财务表中计算工资")
            input("按回车键退出...")
            return
        
        print(f"计算出 {len(salary_data)} 个人的工资")
        total_salary = sum(salary_data.values())
        print(f"工资总额: {total_salary:,.2f} 元")
        
        # 读取原工资表进行验算
        print("\n🔍 第二步：执行验算...")
        original_data = read_payroll_csv(payroll_csv)
        verification_result = perform_verification(salary_data, original_data, order_count)
        
        if not verification_result['passed']:
            print("❌ 验算未通过，发现以下问题：")
            for warning in verification_result['warnings']:
                print(f"⚠️  {warning}")
            
            user_input = input("\n是否继续更新工资表？(y/N): ").strip().lower()
            if user_input != 'y':
                print("操作已取消")
                input("按回车键退出...")
                return
        else:
            print("✅ 验算通过！")
        
        # 更新工资表
        print("\n📝 第三步：更新工资表...")
        update_count, update_details = update_payroll_csv(payroll_csv, salary_data)
        
        # 最终验算
        print("\n🔍 第四步：最终验算...")
        final_verification = perform_final_verification(salary_data, update_details)
        
        if final_verification['passed']:
            print("✅ 最终验算通过！")
            
            # 显示汇总信息
            print_summary(salary_data, update_count, verification_result, update_details)
            
            # 生成验算报告
            generate_verification_report(verification_result, final_verification, 
                                       salary_data, update_details)
        else:
            print("❌ 最终验算失败")
        
    except Exception as e:
        print(f"\n❌ 出现错误: {e}")
        print("请检查文件格式是否正确")
    
    input("\n按回车键退出...")

def select_file_manual(file_type):
    """手动选择文件"""
    print(f"\n📁 选择{file_type}文件:")
    
    # 列出当前目录的相关文件
    patterns = ['*.xlsx', '*.xls', '*.csv']
    files = []
    for pattern in patterns:
        files.extend(glob.glob(pattern))
    
    if files:
        print("当前目录中的文件:")
        for i, file in enumerate(files, 1):
            print(f"  {i}. {file}")
        print(f"  {len(files) + 1}. 手动输入文件路径")
        
        while True:
            try:
                choice = input(f"请选择{file_type}文件 (1-{len(files) + 1}): ").strip()
                
                if choice == str(len(files) + 1):
                    # 手动输入路径
                    file_path = input("请输入文件完整路径: ").strip().strip('"')
                    if os.path.exists(file_path):
                        return file_path
                    else:
                        print("❌ 文件不存在，请重新输入")
                        continue
                
                choice_num = int(choice)
                if 1 <= choice_num <= len(files):
                    return files[choice_num - 1]
                else:
                    print(f"❌ 请输入1-{len(files) + 1}之间的数字")
                    
            except ValueError:
                print("❌ 请输入有效的数字")
    else:
        # 没有找到文件，手动输入
        print("当前目录中未找到Excel或CSV文件")
        file_path = input(f"请输入{file_type}文件的完整路径: ").strip().strip('"')
        if os.path.exists(file_path):
            return file_path
        else:
            print("❌ 文件不存在")
            return None

def convert_to_csv(file_path, file_type):
    """将Excel文件转换为CSV格式"""
    try:
        if file_path.lower().endswith('.csv'):
            print(f"✅ {file_type}已是CSV格式")
            return file_path
        
        # 尝试使用pandas转换（如果可用）
        try:
            import pandas as pd
            
            if file_path.lower().endswith('.xlsx'):
                df = pd.read_excel(file_path)
            elif file_path.lower().endswith('.xls'):
                if file_type == "工资表":
                    df = pd.read_excel(file_path, header=1)  # 工资表跳过第一行
                else:
                    df = pd.read_excel(file_path)
            else:
                print(f"❌ 不支持的文件格式: {file_path}")
                return None
            
            csv_path = f"{file_type}_temp.csv"
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"✅ {file_type}已转换为CSV格式")
            return csv_path
            
        except ImportError:
            print("⚠️ 未安装pandas，无法自动转换Excel文件")
            print("请手动将Excel文件转换为CSV格式，或安装pandas:")
            print("pip install pandas openpyxl")
            
            # 询问用户是否有CSV版本
            csv_file = input(f"如果您已有{file_type}的CSV版本，请输入路径（否则按回车跳过）: ").strip()
            if csv_file and os.path.exists(csv_file):
                return csv_file
            return None
            
    except Exception as e:
        print(f"❌ 转换{file_type}时出错: {e}")
        return None

def calculate_salary_from_csv(csv_file):
    """从CSV格式的财务表计算工资"""
    salary_data = {}
    order_count = {}

    try:
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            # 尝试自动检测分隔符
            sample = f.read(1024)
            f.seek(0)

            # 检测分隔符
            delimiter = ','
            if sample.count('\t') > sample.count(','):
                delimiter = '\t'

            reader = csv.DictReader(f, delimiter=delimiter)

            # 查找相关列名
            fieldnames = reader.fieldnames
            name_col = None
            salary_col = None

            # 查找姓名列
            for field in fieldnames:
                if 'pw姓名' in field or '姓名' in field or 'name' in field.lower():
                    name_col = field
                    break

            # 查找工资列
            for field in fieldnames:
                if '实发工资' in field or '工资' in field or 'salary' in field.lower():
                    salary_col = field
                    break

            if not name_col or not salary_col:
                print(f"❌ 无法找到必要的列")
                print(f"可用列: {fieldnames}")
                print(f"需要包含姓名和工资相关的列")
                return {}, {}

            print(f"使用列: 姓名={name_col}, 工资={salary_col}")

            # 读取数据并汇总
            total_records = 0
            valid_records = 0

            for row in reader:
                total_records += 1
                name = row.get(name_col, '').strip()
                salary_str = row.get(salary_col, '0').strip()

                if name and salary_str and name != name_col:  # 排除表头重复
                    try:
                        # 清理工资数据
                        salary_str = salary_str.replace(',', '').replace('￥', '').replace('元', '')
                        salary = float(salary_str)

                        if salary > 0:  # 只计算正数工资
                            if name in salary_data:
                                salary_data[name] += salary
                                order_count[name] += 1
                            else:
                                salary_data[name] = salary
                                order_count[name] = 1
                            valid_records += 1
                    except ValueError:
                        continue

            print(f"财务表总记录数: {total_records}")
            print(f"有效记录数: {valid_records}")
            print(f"过滤掉的记录数: {total_records - valid_records}")

        return salary_data, order_count

    except Exception as e:
        print(f"读取财务表时出错: {e}")
        return {}, {}

def read_payroll_csv(csv_file):
    """读取工资表数据"""
    payroll_data = {}

    try:
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            sample = f.read(1024)
            f.seek(0)
            delimiter = ','
            if sample.count('\t') > sample.count(','):
                delimiter = '\t'

            reader = csv.DictReader(f, delimiter=delimiter)

            for row in reader:
                remark = row.get('备注（选填）', '').strip()
                amount_str = row.get('金额（必填，单位：元）', '0').strip()

                if remark and amount_str:
                    try:
                        amount = float(amount_str.replace(',', ''))
                        payroll_data[remark] = amount
                    except ValueError:
                        continue

        return payroll_data

    except Exception as e:
        print(f"读取工资表时出错: {e}")
        return {}

def perform_verification(salary_data, original_data, order_count):
    """执行验算"""
    warnings = []
    passed = True

    print("  🔍 验算项目1: 数据完整性检查")

    # 检查1: 财务表数据完整性
    total_orders = sum(order_count.values())
    unique_employees = len(salary_data)
    avg_orders_per_employee = total_orders / unique_employees if unique_employees > 0 else 0

    print(f"    - 总订单数: {total_orders}")
    print(f"    - 员工数量: {unique_employees}")
    print(f"    - 平均每人订单数: {avg_orders_per_employee:.1f}")

    if avg_orders_per_employee < 1:
        warnings.append("平均每人订单数过少，请检查数据")

    # 检查2: 工资合理性检查
    print("  🔍 验算项目2: 工资合理性检查")

    if salary_data:
        max_salary = max(salary_data.values())
        min_salary = min(salary_data.values())
        avg_salary = sum(salary_data.values()) / len(salary_data)

        print(f"    - 最高工资: {max_salary:,.2f} 元")
        print(f"    - 最低工资: {min_salary:,.2f} 元")
        print(f"    - 平均工资: {avg_salary:,.2f} 元")

        if max_salary > 100000:  # 10万以上
            warnings.append(f"存在异常高工资: {max_salary:,.2f} 元")

        if min_salary < 0:
            warnings.append(f"存在负数工资: {min_salary:,.2f} 元")
            passed = False

    # 检查3: 匹配度检查
    print("  🔍 验算项目3: 数据匹配度检查")

    finance_names = set(salary_data.keys())
    payroll_names = set(original_data.keys())

    matched_count = len(finance_names & payroll_names)
    total_payroll = len(payroll_names)
    match_rate = (matched_count / total_payroll * 100) if total_payroll > 0 else 0

    print(f"    - 工资表记录数: {total_payroll}")
    print(f"    - 匹配成功数: {matched_count}")
    print(f"    - 匹配率: {match_rate:.1f}%")

    if match_rate < 80:
        warnings.append(f"匹配率过低: {match_rate:.1f}%")

    # 检查4: 金额变化检查
    print("  🔍 验算项目4: 金额变化检查")

    original_total = sum(original_data.values())
    new_total = sum(salary_data[name] for name in finance_names & payroll_names)
    change_amount = new_total - original_total
    change_rate = (change_amount / original_total * 100) if original_total > 0 else 0

    print(f"    - 原总额: {original_total:,.2f} 元")
    print(f"    - 新总额: {new_total:,.2f} 元")
    print(f"    - 变化金额: {change_amount:+,.2f} 元")
    print(f"    - 变化率: {change_rate:+.1f}%")

    if abs(change_rate) > 50:  # 变化超过50%
        warnings.append(f"工资总额变化过大: {change_rate:+.1f}%")

    return {
        'passed': passed and len(warnings) == 0,
        'warnings': warnings,
        'stats': {
            'total_orders': total_orders,
            'unique_employees': unique_employees,
            'max_salary': max(salary_data.values()) if salary_data else 0,
            'min_salary': min(salary_data.values()) if salary_data else 0,
            'avg_salary': sum(salary_data.values()) / len(salary_data) if salary_data else 0,
            'match_rate': match_rate,
            'change_rate': change_rate,
            'original_total': original_total,
            'new_total': new_total
        }
    }

def update_payroll_csv(csv_file, salary_data):
    """更新CSV格式的工资表"""

    try:
        # 读取原工资表
        rows = []
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            # 检测分隔符
            sample = f.read(1024)
            f.seek(0)
            delimiter = ','
            if sample.count('\t') > sample.count(','):
                delimiter = '\t'

            reader = csv.reader(f, delimiter=delimiter)
            rows = list(reader)

        if len(rows) < 2:
            print("❌ 工资表格式不正确")
            return 0, {}

        # 查找列索引
        header = rows[0] if rows else []
        name_col_idx = None
        amount_col_idx = None
        remark_col_idx = None

        for i, col in enumerate(header):
            if '姓名' in col:
                name_col_idx = i
            elif '金额' in col or '工资' in col:
                amount_col_idx = i
            elif '备注' in col:
                remark_col_idx = i

        if amount_col_idx is None:
            print("❌ 无法找到金额列")
            return 0, {}

        update_count = 0
        update_details = {
            'updated_records': [],
            'unchanged_records': [],
            'error_records': []
        }

        # 更新数据行
        for i in range(1, len(rows)):  # 跳过表头
            row = rows[i]
            if len(row) <= max(amount_col_idx, remark_col_idx or 0):
                continue

            # 查找匹配的员工
            remark = row[remark_col_idx] if remark_col_idx is not None else ''

            if remark in salary_data:
                old_amount = 0
                try:
                    old_amount = float(row[amount_col_idx].replace(',', '')) if row[amount_col_idx] else 0
                except ValueError:
                    old_amount = 0

                new_amount = salary_data[remark]

                # 更新金额
                row[amount_col_idx] = str(new_amount)

                update_details['updated_records'].append({
                    'name': remark,
                    'old_amount': old_amount,
                    'new_amount': new_amount,
                    'difference': new_amount - old_amount
                })

                if abs(new_amount - old_amount) > 0.01:  # 有实际变化
                    update_count += 1
                    print(f"更新: {remark} -> {new_amount:,.2f}元")
                else:
                    update_details['unchanged_records'].append(remark)
            else:
                update_details['error_records'].append(remark)

        # 保存更新后的文件
        output_file = '工资表_更新.csv'
        with open(output_file, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)

        print(f"✅ 已保存为: {output_file}")

        return update_count, update_details

    except Exception as e:
        print(f"更新工资表时出错: {e}")
        return 0, {}

def perform_final_verification(salary_data, update_details):
    """最终验算"""
    print("  🔍 最终验算: 数据一致性检查")

    # 计算预期总额和实际更新总额
    updated_records = update_details.get('updated_records', [])
    expected_total = sum(record['new_amount'] for record in updated_records)
    actual_total = sum(salary_data[record['name']] for record in updated_records)

    difference = abs(expected_total - actual_total)

    print(f"    - 预期总额: {expected_total:,.2f} 元")
    print(f"    - 实际总额: {actual_total:,.2f} 元")
    print(f"    - 差异: {difference:,.2f} 元")
    print(f"    - 更新记录数: {len(updated_records)}")
    print(f"    - 未变化记录数: {len(update_details.get('unchanged_records', []))}")
    print(f"    - 错误记录数: {len(update_details.get('error_records', []))}")

    passed = difference < 0.01  # 允许0.01元的浮点误差

    return {
        'passed': passed,
        'expected_total': expected_total,
        'actual_total': actual_total,
        'difference': difference
    }

def print_summary(salary_data, update_count, verification_result, update_details):
    """打印汇总信息"""

    print(f"\n📊 最终汇总:")
    print(f"   - 员工总数: {len(salary_data)}")
    print(f"   - 工资总额: {sum(salary_data.values()):,.2f} 元")
    print(f"   - 更新记录: {update_count}")
    print(f"   - 验算状态: {'通过' if verification_result['passed'] else '有警告'}")

    if verification_result['stats']:
        stats = verification_result['stats']
        print(f"   - 原总额: {stats['original_total']:,.2f} 元")
        print(f"   - 变化: {stats['new_total'] - stats['original_total']:+,.2f} 元")

    print(f"\n🏆 工资排行榜 (前10名):")
    sorted_salary = sorted(salary_data.items(), key=lambda x: x[1], reverse=True)
    for i, (name, amount) in enumerate(sorted_salary[:10], 1):
        print(f"   {i:2d}. {name:<20} {amount:>10,.2f}元")

    print(f"\n✅ 工资计算完成！所有验算通过！")

def generate_verification_report(verification_result, final_verification, salary_data, update_details):
    """生成验算报告"""

    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    report = f"""
=== 便携版工资计算器v2验算报告 ===
生成时间: {timestamp}

📊 数据统计:
- 员工总数: {len(salary_data)}
- 工资总额: {sum(salary_data.values()):,.2f} 元
- 平均工资: {sum(salary_data.values()) / len(salary_data):,.2f} 元

🔍 验算结果:
- 初步验算: {'通过' if verification_result['passed'] else '未通过'}
- 最终验算: {'通过' if final_verification['passed'] else '未通过'}
- 数据差异: {final_verification['difference']:,.2f} 元

📝 更新统计:
- 更新记录数: {len(update_details.get('updated_records', []))}
- 未变化记录数: {len(update_details.get('unchanged_records', []))}
- 错误记录数: {len(update_details.get('error_records', []))}

⚠️ 警告信息:
"""

    if verification_result['warnings']:
        for warning in verification_result['warnings']:
            report += f"- {warning}\n"
    else:
        report += "- 无警告\n"

    if verification_result['stats']:
        stats = verification_result['stats']
        report += f"""
📈 详细统计:
- 总订单数: {stats['total_orders']}
- 最高工资: {stats['max_salary']:,.2f} 元
- 最低工资: {stats['min_salary']:,.2f} 元
- 匹配率: {stats['match_rate']:.1f}%
- 变化率: {stats['change_rate']:+.1f}%
"""

    report += f"""
🏆 工资排行榜 (前10名):
"""

    sorted_salary = sorted(salary_data.items(), key=lambda x: x[1], reverse=True)
    for i, (name, amount) in enumerate(sorted_salary[:10], 1):
        report += f"{i:2d}. {name:<20} {amount:>10,.2f}元\n"

    # 保存报告
    report_file = f'便携版工资验算报告v2_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"✅ 已生成验算报告: {report_file}")
    except Exception as e:
        print(f"⚠️ 保存验算报告失败: {e}")

if __name__ == "__main__":
    main()
