"""
便携版工资计算器 - 无需额外依赖
使用标准库实现Excel文件读取和工资计算
"""

import csv
import os
import shutil
from datetime import datetime
import json

def main():
    """主函数"""
    print("=== 便携版工资计算器 ===")
    print("注意：请先将Excel文件转换为CSV格式")
    print("1. 打开财务表.xlsx，另存为 财务表.csv")
    print("2. 打开工资表.xls，另存为 工资表.csv")
    print("3. 确保CSV文件使用UTF-8编码")
    print()
    
    # 检查文件是否存在
    if not os.path.exists('财务表.csv'):
        print("❌ 未找到 财务表.csv 文件")
        print("请将财务表.xlsx转换为CSV格式")
        input("按回车键退出...")
        return
    
    if not os.path.exists('工资表.csv'):
        print("❌ 未找到 工资表.csv 文件")
        print("请将工资表.xls转换为CSV格式")
        input("按回车键退出...")
        return
    
    try:
        # 备份原文件
        backup_name = f'工资表_备份_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        shutil.copy2('工资表.csv', backup_name)
        print(f"✅ 已备份原工资表为: {backup_name}")
        
        # 读取财务表并计算工资
        print("\n📊 读取财务表并计算工资...")
        salary_data = calculate_salary_from_csv()
        
        if not salary_data:
            print("❌ 无法从财务表中计算工资")
            input("按回车键退出...")
            return
        
        print(f"计算出 {len(salary_data)} 个人的工资")
        total_salary = sum(salary_data.values())
        print(f"工资总额: {total_salary:,.2f} 元")
        
        # 更新工资表
        print("\n📝 更新工资表...")
        update_count = update_payroll_csv(salary_data)
        
        print(f"更新了 {update_count} 条工资记录")
        
        # 显示工资排行
        print("\n🏆 工资排行榜 (前10名):")
        sorted_salary = sorted(salary_data.items(), key=lambda x: x[1], reverse=True)
        for i, (name, amount) in enumerate(sorted_salary[:10], 1):
            print(f"   {i:2d}. {name:<20} {amount:>10,.2f}元")
        
        print(f"\n✅ 工资计算完成！")
        print(f"📁 生成文件:")
        print(f"   - {backup_name} (原文件备份)")
        print(f"   - 工资表_更新.csv (更新后的工资表)")
        
    except Exception as e:
        print(f"\n❌ 出现错误: {e}")
        print("请检查CSV文件格式是否正确")
    
    input("\n按回车键退出...")

def calculate_salary_from_csv():
    """从CSV格式的财务表计算工资"""
    salary_data = {}
    
    try:
        with open('财务表.csv', 'r', encoding='utf-8-sig') as f:
            # 尝试自动检测分隔符
            sample = f.read(1024)
            f.seek(0)
            
            # 检测分隔符
            delimiter = ','
            if sample.count('\t') > sample.count(','):
                delimiter = '\t'
            
            reader = csv.DictReader(f, delimiter=delimiter)
            
            # 查找相关列名
            fieldnames = reader.fieldnames
            name_col = None
            salary_col = None
            
            # 查找姓名列
            for field in fieldnames:
                if 'pw姓名' in field or '姓名' in field or 'name' in field.lower():
                    name_col = field
                    break
            
            # 查找工资列
            for field in fieldnames:
                if '实发工资' in field or '工资' in field or 'salary' in field.lower():
                    salary_col = field
                    break
            
            if not name_col or not salary_col:
                print(f"❌ 无法找到必要的列")
                print(f"可用列: {fieldnames}")
                print(f"需要包含姓名和工资相关的列")
                return {}
            
            print(f"使用列: 姓名={name_col}, 工资={salary_col}")
            
            # 读取数据并汇总
            for row in reader:
                name = row.get(name_col, '').strip()
                salary_str = row.get(salary_col, '0').strip()
                
                if name and salary_str:
                    try:
                        # 清理工资数据
                        salary_str = salary_str.replace(',', '').replace('￥', '').replace('元', '')
                        salary = float(salary_str)
                        
                        if name in salary_data:
                            salary_data[name] += salary
                        else:
                            salary_data[name] = salary
                    except ValueError:
                        continue
        
        return salary_data
        
    except Exception as e:
        print(f"读取财务表时出错: {e}")
        return {}

def update_payroll_csv(salary_data):
    """更新CSV格式的工资表"""
    
    try:
        # 读取原工资表
        rows = []
        with open('工资表.csv', 'r', encoding='utf-8-sig') as f:
            # 检测分隔符
            sample = f.read(1024)
            f.seek(0)
            delimiter = ','
            if sample.count('\t') > sample.count(','):
                delimiter = '\t'
            
            reader = csv.reader(f, delimiter=delimiter)
            rows = list(reader)
        
        if len(rows) < 2:
            print("❌ 工资表格式不正确")
            return 0
        
        # 查找列索引
        header = rows[0] if rows else []
        name_col_idx = None
        amount_col_idx = None
        remark_col_idx = None
        
        for i, col in enumerate(header):
            if '姓名' in col:
                name_col_idx = i
            elif '金额' in col or '工资' in col:
                amount_col_idx = i
            elif '备注' in col:
                remark_col_idx = i
        
        if amount_col_idx is None:
            print("❌ 无法找到金额列")
            return 0
        
        update_count = 0
        
        # 更新数据行
        for i in range(1, len(rows)):  # 跳过表头
            row = rows[i]
            if len(row) <= max(amount_col_idx, remark_col_idx or 0):
                continue
            
            # 查找匹配的员工
            remark = row[remark_col_idx] if remark_col_idx is not None else ''
            
            if remark in salary_data:
                # 更新金额
                row[amount_col_idx] = str(salary_data[remark])
                update_count += 1
                print(f"更新: {remark} -> {salary_data[remark]:,.2f}元")
        
        # 保存更新后的文件
        with open('工资表_更新.csv', 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(rows)
        
        return update_count
        
    except Exception as e:
        print(f"更新工资表时出错: {e}")
        return 0

if __name__ == "__main__":
    main()
