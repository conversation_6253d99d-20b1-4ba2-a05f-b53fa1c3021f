from cx_Freeze import setup, Executable
import sys

# Dependencies are automatically detected, but it might need fine tuning.
build_options = {
    'packages': ['csv', 'os', 'shutil', 'datetime', 'glob'],
    'excludes': ['tkinter', 'matplotlib', 'numpy', 'scipy'],
    'include_files': [],
    'optimize': 2
}

base = 'Console'

executables = [
    Executable('salary_calculator_v2.py', 
              base=base, 
              target_name='工资计算器v2-完整功能版.exe',
              icon=None)
]

setup(
    name='工资计算器v2',
    version='2.0',
    description='便携版工资计算器v2 - 完整功能版',
    options={'build_exe': build_options},
    executables=executables
)
