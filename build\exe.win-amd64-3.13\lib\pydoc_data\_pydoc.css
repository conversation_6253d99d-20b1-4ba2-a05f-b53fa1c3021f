/*
    CSS file for pydoc.

    Contents of this file are subject to change without notice.

*/

body {
    background-color: #f0f0f8;
}

table.heading tr {
    background-color: #7799ee;
}

.decor {
    color: #ffffff;
}

.title-decor {
    background-color: #ffc8d8;
    color: #000000;
}

.pkg-content-decor {
    background-color: #aa55cc;
}

.index-decor {
    background-color: #ee77aa;
}

.functions-decor {
    background-color: #eeaa77;
}

.data-decor {
    background-color: #55aa55;
}

.author-decor {
    background-color: #7799ee;
}

.credits-decor {
    background-color: #7799ee;
}

.error-decor {
    background-color: #bb0000;
}

.grey {
    color: #909090;
}

.white {
    color: #ffffff;
}

.repr {
    color: #c040c0;
}

table.heading tr td.title {
    vertical-align: bottom;
}

table.heading tr td.extra {
    vertical-align: bottom;
    text-align: right;
}

.heading-text {
    font-family: helvetica, arial;
}

.bigsection {
    font-size: larger;
}

.title {
    font-size: x-large;
}

.code {
    font-family: monospace;
}

table {
    width: 100%;
    border-spacing : 0;
    border-collapse : collapse;
    border: 0;
}

td {
    padding: 2;
}

td.section-title {
    vertical-align: bottom;
}

td.multicolumn {
    width: 25%;
    vertical-align: bottom;
}

td.singlecolumn {
    width: 100%;
}
