# 工资计算器可执行文件使用说明

## 📦 可执行文件列表

### 1. 便携版工资计算器.exe ⭐ **推荐**
- **位置**: `dist/便携版工资计算器.exe`
- **特点**: 无需Python环境，独立运行
- **依赖**: 仅需要CSV文件（Excel转换而来）
- **大小**: 约5MB
- **适用**: 所有Windows用户

## 🚀 使用步骤

### 第一步：准备CSV文件

1. **转换财务表**：
   - 打开 `财务表.xlsx`
   - 点击 `文件` → `另存为`
   - 选择 `CSV UTF-8（逗号分隔）(*.csv)` 格式
   - 保存为 `财务表.csv`

2. **转换工资表**：
   - 打开 `工资表.xls`
   - 点击 `文件` → `另存为`
   - 选择 `CSV UTF-8（逗号分隔）(*.csv)` 格式
   - 保存为 `工资表.csv`

### 第二步：运行程序

1. 将 `便携版工资计算器.exe` 复制到包含CSV文件的目录
2. 双击运行 `便携版工资计算器.exe`
3. 按照程序提示操作

### 第三步：查看结果

程序会生成：
- `工资表_备份_[时间戳].csv` - 原工资表备份
- `工资表_更新.csv` - 更新后的工资表

## 📋 文件结构示例

```
工作目录/
├── 便携版工资计算器.exe
├── 财务表.csv
├── 工资表.csv
├── 工资表_备份_20250618_123456.csv  (自动生成)
└── 工资表_更新.csv                   (自动生成)
```

## ⚠️ 注意事项

### CSV转换要点
1. **编码格式**: 必须使用 `UTF-8` 编码
2. **分隔符**: 使用逗号分隔
3. **列名保持**: 确保列名包含关键字：
   - 财务表：`pw姓名`、`实发工资`
   - 工资表：`备注`、`金额`

### 常见问题解决

1. **"未找到CSV文件"**
   - 检查文件名是否正确
   - 确保CSV文件与exe在同一目录

2. **"无法找到必要的列"**
   - 检查CSV文件是否包含必要的列名
   - 确保列名中包含关键字（姓名、工资等）

3. **"中文显示乱码"**
   - 重新保存CSV文件，选择UTF-8编码
   - 或在Excel中选择"CSV UTF-8"格式

## 🔧 高级用户选项

### 如果您有Python环境

可以直接运行Python脚本版本：
```bash
python 便携版工资计算器.py
```

### 重新打包

如果需要修改程序并重新打包：
```bash
pip install pyinstaller
python -m PyInstaller --onefile --console "便携版工资计算器.py"
```

## 📊 运行示例

```
=== 便携版工资计算器 ===
注意：请先将Excel文件转换为CSV格式
1. 打开财务表.xlsx，另存为 财务表.csv
2. 打开工资表.xls，另存为 工资表.csv
3. 确保CSV文件使用UTF-8编码

✅ 已备份原工资表为: 工资表_备份_20250618_123456.csv

📊 读取财务表并计算工资...
使用列: 姓名=pw姓名, 工资=实发工资
计算出 398 个人的工资
工资总额: 1,160,853.86 元

📝 更新工资表...
更新: 小里-顾乔 -> 46,527.82元
更新: 小里-77 -> 39,239.80元
...
更新了 385 条工资记录

🏆 工资排行榜 (前10名):
    1. 小里-顾乔                 46,527.82元
    2. 小里-77                 39,239.80元
    3. 小里-TzT                13,851.60元
    ...

✅ 工资计算完成！
📁 生成文件:
   - 工资表_备份_20250618_123456.csv (原文件备份)
   - 工资表_更新.csv (更新后的工资表)

按回车键退出...
```

## 🎯 优势

1. **无需安装**: 不需要Python环境
2. **体积小**: 仅5MB左右
3. **速度快**: 直接处理CSV文件
4. **兼容性好**: 支持所有Windows系统
5. **安全可靠**: 自动备份原文件

## 📞 技术支持

如遇问题，请检查：
1. CSV文件格式是否正确
2. 文件编码是否为UTF-8
3. 列名是否包含必要的关键字
4. 文件是否在同一目录下

---

**推荐使用便携版工资计算器.exe，简单易用，无需任何额外安装！**
