# Config file for running mypy on tomllib.
# Run mypy by invoking `mypy --config-file Lib/tomllib/mypy.ini`
# on the command-line from the repo root

[mypy]
files = Lib/tomllib
mypy_path = $MYPY_CONFIG_FILE_DIR/../../Misc/mypy
explicit_package_bases = True
python_version = 3.12
pretty = True

# Enable most stricter settings
enable_error_code = ignore-without-code
strict = True
strict_bytes = True
local_partial_types = True
warn_unreachable = True
